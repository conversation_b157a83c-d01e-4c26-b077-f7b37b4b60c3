-- Fix the search_path security issue by updating the cleanup function
CREATE OR REP<PERSON>CE FUNCTION public.cleanup_old_conversions()
RETURNS TEXT AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete conversions older than 7 days
    DELETE FROM public.conversions 
    WHER<PERSON> created_at < NOW() - INTERVAL '7 days';
    
    -- Get the number of deleted rows
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO public.cleanup_logs (operation, deleted_count, executed_at)
    VALUES ('cleanup_old_conversions', deleted_count, NOW())
    ON CONFLICT DO NOTHING; -- Ignore if cleanup_logs table doesn't exist
    
    -- Return summary
    RETURN format('Cleaned up %s old conversion records older than 7 days', deleted_count);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;