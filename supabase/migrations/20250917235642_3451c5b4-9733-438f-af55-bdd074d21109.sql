-- Enable pg_cron extension for scheduled jobs
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- <PERSON>reate function to cleanup old conversions (older than 7 days)
CREATE OR REPLACE FUNCTION public.cleanup_old_conversions()
RETURNS TEXT AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete conversions older than 7 days
    DELETE FROM public.conversions 
    WH<PERSON><PERSON> created_at < NOW() - INTERVAL '7 days';
    
    -- Get the number of deleted rows
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO public.cleanup_logs (operation, deleted_count, executed_at)
    VALUES ('cleanup_old_conversions', deleted_count, NOW())
    ON CONFLICT DO NOTHING; -- Ignore if cleanup_logs table doesn't exist
    
    -- Return summary
    RETURN format('Cleaned up %s old conversion records older than 7 days', deleted_count);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create optional cleanup logs table for monitoring
CREATE TABLE IF NOT EXISTS public.cleanup_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    operation TEXT NOT NULL,
    deleted_count INTEGER NOT NULL,
    executed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Enable RLS on cleanup_logs table
ALTER TABLE public.cleanup_logs ENABLE ROW LEVEL SECURITY;

-- Allow system to insert cleanup logs
CREATE POLICY "System can insert cleanup logs" 
ON public.cleanup_logs 
FOR INSERT 
WITH CHECK (true);

-- Schedule the cleanup function to run daily at 2 AM UTC
SELECT cron.schedule(
    'cleanup-old-conversions-daily',
    '0 2 * * *', -- Daily at 2 AM UTC
    'SELECT public.cleanup_old_conversions();'
);