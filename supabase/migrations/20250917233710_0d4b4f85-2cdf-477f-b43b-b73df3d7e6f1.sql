-- Fix the overly permissive UPDATE policy for conversions table
DROP POLICY IF EXISTS "Public can update conversions" ON public.conversions;

-- Create a more restrictive UPDATE policy that only allows system updates
CREATE POLICY "System can update conversion records" 
ON public.conversions 
FOR UPDATE 
USING (conversion_status = 'processing')
WITH CHECK (conversion_status IN ('completed', 'failed'));