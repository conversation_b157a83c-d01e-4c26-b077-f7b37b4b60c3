-- Fix the overly permissive UPDATE policy for conversions table
DROP POLICY IF EXISTS "Public can update conversions" ON public.conversions;

-- Create a more restrictive UPDATE policy that only allows updating status and content for processing conversions
CREATE POLICY "System can update conversion status and content" 
ON public.conversions 
FOR UPDATE 
USING (conversion_status IN ('processing', 'failed'))
WITH CHECK (conversion_status IN ('completed', 'failed') AND (
  -- Only allow updating these specific fields
  (OLD.conversion_status = 'processing' AND NEW.conversion_status IN ('completed', 'failed')) OR
  (OLD.conversion_status = 'failed')
));