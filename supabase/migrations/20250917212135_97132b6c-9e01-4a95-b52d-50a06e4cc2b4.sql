-- Create a table to store converted HTML files and their metadata
CREATE TABLE public.conversions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  slug TEXT NOT NULL UNIQUE,
  original_filename TEXT NOT NULL,
  file_size_mb DECIMAL(10,2) NOT NULL,
  conversion_status TEXT NOT NULL DEFAULT 'processing' CHECK (conversion_status IN ('processing', 'completed', 'failed')),
  html_content TEXT,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.conversions ENABLE ROW LEVEL SECURITY;

-- Create policy to allow public read access to completed conversions
CREATE POLICY "Public can view completed conversions" 
ON public.conversions 
FOR SELECT 
USING (conversion_status = 'completed');

-- Create policy to allow public insert for new conversions
CREATE POLICY "Public can create conversions" 
ON public.conversions 
FOR INSERT 
WITH CHECK (true);

-- Create policy to allow public update for conversion processing
CREATE POLICY "Public can update conversions" 
ON public.conversions 
FOR UPDATE 
USING (true);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SET search_path = public;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_conversions_updated_at
BEFORE UPDATE ON public.conversions
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Create index for faster slug lookups
CREATE INDEX idx_conversions_slug ON public.conversions(slug);
CREATE INDEX idx_conversions_status ON public.conversions(conversion_status);
CREATE INDEX idx_conversions_created_at ON public.conversions(created_at DESC);