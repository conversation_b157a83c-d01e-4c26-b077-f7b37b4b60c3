import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Starting PDF conversion process");

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
    const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Get the datalab.to API key
    const datalabApiKey = Deno.env.get("DATALAB_API_KEY");
    if (!datalabApiKey) {
      throw new Error("Datalab API key not configured");
    }

    // Parse form data
    const formData = await req.formData();
    const file = formData.get("file") as File;
    const originalFilename = formData.get("originalFilename") as string;
    const fileSizeMB = parseFloat(formData.get("fileSizeMB") as string);

    if (!file || !originalFilename || !fileSizeMB) {
      throw new Error("Missing required fields");
    }

    // Check file size against Edge Function memory constraints
    if (fileSizeMB > 200) {
      throw new Error(
        "File too large for processing. Maximum supported size is 200MB."
      );
    }

    console.log(`Processing file: ${originalFilename} (${fileSizeMB}MB)`);

    // Generate a unique slug for the conversion
    const slug = crypto.randomUUID().replace(/-/g, "").substring(0, 12);

    // Create initial conversion record
    const { data: conversionRecord, error: insertError } = await supabase
      .from("conversions")
      .insert({
        slug,
        original_filename: originalFilename,
        file_size_mb: fileSizeMB,
        conversion_status: "processing",
      })
      .select()
      .single();

    if (insertError) {
      console.error("Error creating conversion record:", insertError);
      throw new Error("Failed to create conversion record");
    }

    console.log(`Created conversion record with slug: ${slug}`);

    // Stream the file directly to datalab.to API to avoid memory issues
    console.log("Sending PDF to datalab.to for conversion");

    // Create a new FormData for the API call using the original file stream
    const datalabFormData = new FormData();
    datalabFormData.append("file", file, originalFilename);
    datalabFormData.append("output_format", "html");

    // Send to datalab.to API for conversion with timeout handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minute timeout

    try {
      const datalabResponse = await fetch(
        "https://www.datalab.to/api/v1/marker",
        {
          method: "POST",
          headers: {
            "X-Api-Key": datalabApiKey,
          },
          body: datalabFormData,
          signal: controller.signal,
        }
      );

      clearTimeout(timeoutId);

      if (!datalabResponse.ok) {
        const errorText = await datalabResponse.text();
        console.error(
          "Datalab API error response:",
          datalabResponse.status,
          errorText
        );

        let userFriendlyMessage =
          "Conversion service is temporarily unavailable";
        if (datalabResponse.status === 520) {
          userFriendlyMessage =
            "The PDF conversion service is temporarily down. Please try again in a few minutes.";
        } else if (datalabResponse.status === 413) {
          userFriendlyMessage =
            "PDF file is too large for conversion. Please try with a smaller file.";
        } else if (datalabResponse.status >= 500) {
          userFriendlyMessage =
            "PDF conversion service is experiencing issues. Please try again later.";
        } else if (
          datalabResponse.status === 400 ||
          datalabResponse.status === 422
        ) {
          userFriendlyMessage =
            "PDF file format is not supported or corrupted. Please try with a different PDF.";
        }

        // Update conversion record with error
        await supabase
          .from("conversions")
          .update({
            conversion_status: "failed",
            error_message: `Datalab API error (${datalabResponse.status}): ${errorText}`,
          })
          .eq("id", conversionRecord.id);

        throw new Error(userFriendlyMessage);
      }

      console.log("PDF conversion API call successful, getting job status");
      const jobResponse = await datalabResponse.json();

      if (!jobResponse.request_check_url) {
        throw new Error(
          "Invalid response from conversion API - no job URL provided"
        );
      }

      console.log(
        `Job submitted, polling URL: ${jobResponse.request_check_url}`
      );

      // Poll the job status until completion
      let attempts = 0;
      const maxAttempts = 60; // 5 minutes with 5-second intervals
      let htmlContent = null;

      while (attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait 5 seconds
        attempts++;

        try {
          const statusResponse = await fetch(jobResponse.request_check_url, {
            method: "GET",
            headers: {
              "X-Api-Key": datalabApiKey,
            },
          });

          if (!statusResponse.ok) {
            console.error(`Status check failed: ${statusResponse.status}`);
            continue;
          }

          const statusData = await statusResponse.json();
          console.log(
            `Job status (attempt ${attempts}):`,
            statusData.status || "unknown"
          );

          if (statusData.status === "complete" && statusData.html) {
            htmlContent = statusData.html;
            break;
          } else if (
            statusData.status === "error" ||
            statusData.status === "failed"
          ) {
            throw new Error(
              `Conversion failed: ${statusData.error || "Unknown error"}`
            );
          }
          // Continue polling if status is 'processing' or similar
        } catch (pollError) {
          console.error(
            `Error polling job status (attempt ${attempts}):`,
            pollError instanceof Error ? pollError.message : String(pollError)
          );
          if (attempts >= maxAttempts - 5) {
            throw pollError; // Only throw on the last few attempts
          }
        }
      }

      if (!htmlContent) {
        throw new Error("Conversion timed out after 5 minutes");
      }

      if (htmlContent.length < 100) {
        throw new Error(
          "Received empty or invalid HTML content from conversion API"
        );
      }

      console.log(
        `HTML content received, size: ${htmlContent.length} characters`
      );

      // Update conversion record with HTML content
      const { error: updateError } = await supabase
        .from("conversions")
        .update({
          conversion_status: "completed",
          html_content: htmlContent,
        })
        .eq("id", conversionRecord.id);

      if (updateError) {
        console.error("Error updating conversion record:", updateError);
        throw new Error("Failed to save HTML content");
      }

      console.log(`Conversion completed successfully for slug: ${slug}`);

      return new Response(
        JSON.stringify({
          success: true,
          slug,
          url: `/view/${slug}`,
          message: "PDF converted successfully",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200,
        }
      );
    } catch (fetchError) {
      clearTimeout(timeoutId);

      if (fetchError instanceof Error && fetchError.name === "AbortError") {
        console.error("Conversion timed out after 5 minutes");
        await supabase
          .from("conversions")
          .update({
            conversion_status: "failed",
            error_message: "Conversion timed out after 5 minutes",
          })
          .eq("id", conversionRecord.id);
        throw new Error(
          "Conversion timed out. Please try with a smaller file."
        );
      }

      throw fetchError;
    }
  } catch (error) {
    console.error("Conversion error:", error instanceof Error ? error.message : String(error));

    // Determine appropriate error message
    let statusCode = 500;
    const errorString = error instanceof Error ? error.message : String(error);
    let errorMessage = "Conversion failed";
    
    if (
      errorString.includes("Memory limit exceeded") ||
      errorString.includes("too large for processing")
    ) {
      errorMessage =
        "File too large for processing. Please try with a smaller PDF (under 200MB).";
      statusCode = 413; // Payload Too Large
    } else if (errorString.includes("timed out")) {
      errorMessage =
        "Conversion timed out. Please try with a smaller or simpler PDF.";
      statusCode = 408; // Request Timeout
    } else if (errorString.includes("Datalab API")) {
      errorMessage = errorString;
      statusCode = 502; // Bad Gateway
    } else if (errorString.includes("API key not configured")) {
      errorMessage = "Service configuration error. Please try again later.";
      statusCode = 503; // Service Unavailable
    } else {
      errorMessage = errorString || "An unexpected error occurred";
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: statusCode,
      }
    );
  }
});
