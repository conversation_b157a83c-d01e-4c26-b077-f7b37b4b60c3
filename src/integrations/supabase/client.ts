// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://xlapcsmgsafcjpvsabug.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhsYXBjc21nc2FmY2pwdnNhYnVnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTgxNDM0NTQsImV4cCI6MjA3MzcxOTQ1NH0.M8fEL6bSZK2HK9KNrkyPySsmisrkrPXw9pNSJiRlQPQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});