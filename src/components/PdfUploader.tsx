import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import {
  Upload,
  FileText,
  CheckCircle,
  XCircle,
  ExternalLink,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface ConversionResult {
  success: boolean;
  slug?: string;
  url?: string;
  error?: string;
}

export const PdfUploader = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [conversionResult, setConversionResult] =
    useState<ConversionResult | null>(null);
  const { toast } = useToast();

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];

      if (!file) return;

      // Validate file type
      if (file.type !== "application/pdf") {
        toast({
          title: "Invalid file type",
          description: "Please upload a PDF file only.",
          variant: "destructive",
        });
        return;
      }

      // Validate file size (200MB limit)
      const maxSizeMB = 200;
      const fileSizeMB = file.size / (1024 * 1024);

      if (fileSizeMB > maxSizeMB) {
        toast({
          title: "File too large",
          description: `File size must be under ${maxSizeMB}MB. Your file is ${fileSizeMB.toFixed(
            1
          )}MB.`,
          variant: "destructive",
        });
        return;
      }

      await convertPdf(file);
    },
    [toast]
  );

  const convertPdf = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);
    setConversionResult(null);

    try {
      const fileSizeMB = file.size / (1024 * 1024);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 500);

      // Prepare form data
      const formData = new FormData();
      formData.append("file", file);
      formData.append("originalFilename", file.name);
      formData.append("fileSizeMB", fileSizeMB.toString());

      // Call the edge function with simple retry and better error surfacing
      let response = await supabase.functions.invoke("convert-pdf", {
        body: formData,
      });
      if (response.error) {
        // one retry for transient issues (e.g., Cloudflare 520)
        await new Promise((r) => setTimeout(r, 1500));
        response = await supabase.functions.invoke("convert-pdf", {
          body: formData,
        });
      }

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.error) {
        const err: any = response.error as any;
        let message: string = err?.message || "Conversion failed";
        const body = err?.context?.body;
        if (body) {
          try {
            const parsed = JSON.parse(body);
            if (parsed?.error) message = parsed.error;
          } catch {
            if (typeof body === "string") {
              if (
                body.includes("Cloudflare") ||
                body.includes("Error code 520")
              ) {
                message =
                  "The conversion service is temporarily unavailable (520). Please try again shortly.";
              } else if (body.length < 500) {
                message = body;
              }
            }
          }
        }
        throw new Error(message);
      }

      const result = response.data as ConversionResult;

      if (result?.success) {
        setConversionResult(result);
        toast({
          title: "Conversion successful!",
          description:
            "Your PDF has been converted to HTML and is now available.",
        });
      } else {
        throw new Error(result?.error || "Conversion failed");
      }
    } catch (error) {
      console.error("Conversion error:", error);
      setConversionResult({
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      });
      toast({
        title: "Conversion failed",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
    },
    multiple: false,
    maxSize: 200 * 1024 * 1024, // 200MB
  });

  const openConvertedFile = () => {
    if (conversionResult?.url) {
      const fullUrl = `${window.location.origin}${conversionResult.url}`;
      window.open(fullUrl, "_blank");
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto space-y-6">
      <Card className="p-8 transition-all duration-300 hover:shadow-xl bg-gradient-to-br from-card to-card/80 border-2">
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-xl p-12 text-center cursor-pointer
            transition-all duration-300 ease-in-out
            ${
              isDragActive
                ? "border-primary bg-gradient-to-br from-primary/10 to-accent/10 scale-[1.02]"
                : "border-border hover:border-primary/50 hover:bg-gradient-to-br hover:from-primary/5 hover:to-accent/5"
            }
          `}
        >
          <input {...getInputProps()} />

          <div className="space-y-4">
            <div
              className={`mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center transition-transform duration-300 ${
                isDragActive ? "scale-110" : ""
              }`}
            >
              <Upload className="w-8 h-8 text-primary-foreground" />
            </div>

            <div>
              <h3 className="text-xl font-semibold text-foreground mb-2">
                {isDragActive ? "Drop your PDF here!" : "Upload your PDF file"}
              </h3>
              <p className="text-muted-foreground">
                Drag and drop a PDF file here, or click to select
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Maximum file size: 200MB • PDF files only
              </p>
            </div>
          </div>
        </div>
      </Card>

      {isUploading && (
        <Card className="p-6 bg-gradient-to-r from-card to-secondary/20">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <FileText className="w-5 h-5 text-primary animate-pulse" />
              <span className="font-medium">Converting your PDF...</span>
            </div>
            <Progress value={uploadProgress} className="w-full" />
            <p className="text-sm text-muted-foreground">
              This may take a few moments depending on file size and complexity.
            </p>
          </div>
        </Card>
      )}

      {conversionResult && (
        <Card
          className={`p-6 transition-all duration-500 ${
            conversionResult.success
              ? "bg-gradient-to-r from-green-500/10 to-emerald-500/10 border-green-500/20"
              : "bg-gradient-to-r from-red-500/10 to-pink-500/10 border-red-500/20"
          }`}
        >
          <div className="flex items-start gap-4">
            {conversionResult.success ? (
              <CheckCircle className="w-6 h-6 text-green-500 mt-1 flex-shrink-0" />
            ) : (
              <XCircle className="w-6 h-6 text-red-500 mt-1 flex-shrink-0" />
            )}

            <div className="flex-1 space-y-3">
              <h4 className="font-semibold text-lg">
                {conversionResult.success
                  ? "Conversion Successful!"
                  : "Conversion Failed"}
              </h4>

              {conversionResult.success ? (
                <div className="space-y-3">
                  <p className="text-muted-foreground">
                    Your PDF has been converted to HTML and is now live on the
                    web.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      onClick={openConvertedFile}
                      className="bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90"
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Converted HTML
                    </Button>
                    <div className="flex items-center gap-2 px-3 py-2 bg-secondary/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">
                        URL:
                      </span>
                      <code className="text-sm font-mono text-foreground bg-background/50 px-2 py-1 rounded">
                        {window.location.origin}
                        {conversionResult.url}
                      </code>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">
                  {conversionResult.error}
                </p>
              )}
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};
