import { useState, useEffect } from 'react';
import { FileText, Clock, CheckCircle, XCircle, ExternalLink } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { supabase } from '@/integrations/supabase/client';

interface Conversion {
  id: string;
  slug: string;
  original_filename: string;
  file_size_mb: number;
  conversion_status: 'processing' | 'completed' | 'failed';
  error_message?: string;
  created_at: string;
}

export const ConversionHistory = () => {
  const [conversions, setConversions] = useState<Conversion[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchConversions();
  }, []);

  const fetchConversions = async () => {
    try {
      // Get conversions from the past week
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      
      const { data, error } = await supabase
        .from('conversions')
        .select('*')
        .gte('created_at', oneWeekAgo.toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching conversions:', error);
        return;
      }

      setConversions(data as Conversion[] || []);
    } catch (error) {
      console.error('Error fetching conversions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatFileSize = (sizeMB: number) => {
    if (sizeMB < 1) {
      return `${(sizeMB * 1024).toFixed(0)} KB`;
    }
    return `${sizeMB.toFixed(1)} MB`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processing':
        return <Clock className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <FileText className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const openConversion = (slug: string) => {
    window.open(`/view/${slug}`, '_blank');
  };

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-secondary rounded w-1/3"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-secondary/50 rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (conversions.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
          <FileText className="w-5 h-5" />
          Conversion History (Past Week)
        </h3>
        <div className="text-center py-8">
          <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">
            No conversions in the past week. Upload a PDF to get started.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
        <FileText className="w-5 h-5" />
        Conversion History (Past Week)
      </h3>
      
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>File Name</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>HTML Link</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {conversions.map((conversion) => (
            <TableRow key={conversion.id}>
              <TableCell className="font-medium">
                <div className="max-w-[300px] truncate" title={conversion.original_filename}>
                  {conversion.original_filename}
                </div>
              </TableCell>
              <TableCell>{formatDate(conversion.created_at)}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  {getStatusIcon(conversion.conversion_status)}
                  <span className="capitalize">{conversion.conversion_status}</span>
                </div>
              </TableCell>
              <TableCell>
                {conversion.conversion_status === 'completed' ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openConversion(conversion.slug)}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View HTML
                  </Button>
                ) : conversion.conversion_status === 'failed' ? (
                  <span className="text-red-500 text-sm">Failed</span>
                ) : (
                  <span className="text-blue-500 text-sm">Processing...</span>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Card>
  );
};