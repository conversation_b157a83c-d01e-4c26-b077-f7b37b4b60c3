import { ReactNode, useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';

interface AuthGuardProps {
  children: ReactNode;
}

export const AuthGuard = ({ children }: AuthGuardProps) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  useEffect(() => {
    // Check if user is authenticated from localStorage
    const authStatus = localStorage.getItem('pdf-converter-auth');
    setIsAuthenticated(authStatus === 'authenticated');
  }, []);

  // Still checking auth status
  if (isAuthenticated === null) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background to-secondary/20">
        <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  // Not authenticated - redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Authenticated - render protected content
  return <>{children}</>;
};

export const checkAuth = (): boolean => {
  return localStorage.getItem('pdf-converter-auth') === 'authenticated';
};

export const setAuth = (authenticated: boolean): void => {
  if (authenticated) {
    localStorage.setItem('pdf-converter-auth', 'authenticated');
  } else {
    localStorage.removeItem('pdf-converter-auth');
  }
};

export const logout = (): void => {
  localStorage.removeItem('pdf-converter-auth');
  window.location.href = '/login';
};