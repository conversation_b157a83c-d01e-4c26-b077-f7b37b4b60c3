import { PdfUploader } from '@/components/PdfUploader';
import { ConversionHistory } from '@/components/ConversionHistory';
import { Button } from '@/components/ui/button';
import { LogOut } from 'lucide-react';
import { logout } from '@/components/AuthGuard';

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-secondary/20">
      {/* Logout button in top-right corner */}
      <div className="absolute top-4 right-4">
        <Button
          variant="outline"
          size="sm"
          onClick={logout}
          className="bg-background/80 backdrop-blur-sm hover:bg-background/90"
        >
          <LogOut className="w-4 h-4 mr-2" />
          Logout
        </Button>
      </div>
      
      {/* Main content */}
      <div className="min-h-screen flex items-center justify-center">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="space-y-8">
            <PdfUploader />
            <ConversionHistory />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
