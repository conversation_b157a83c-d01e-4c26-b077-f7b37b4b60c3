import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Home, AlertCircle } from 'lucide-react';

interface Conversion {
  id: string;
  slug: string;
  original_filename: string;
  file_size_mb: number;
  conversion_status: 'processing' | 'completed' | 'failed';
  html_content?: string;
  error_message?: string;
  created_at: string;
}

const ViewConversion = () => {
  const { slug } = useParams<{ slug: string }>();
  const [conversion, setConversion] = useState<Conversion | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (slug) {
      fetchConversion(slug);
    }
  }, [slug]);

  const fetchConversion = async (conversionSlug: string) => {
    try {
      const { data, error } = await supabase
        .from('conversions')
        .select('*')
        .eq('slug', conversionSlug)
        .eq('conversion_status', 'completed')
        .maybeSingle();

      if (error) {
        console.error('Error fetching conversion:', error);
        setError('Failed to load conversion');
        return;
      }

      if (!data) {
        setError('Conversion not found or not yet completed');
        return;
      }

      setConversion(data as Conversion);
    } catch (error) {
      console.error('Error fetching conversion:', error);
      setError('Failed to load conversion');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-secondary/20 flex items-center justify-center">
        <Card className="p-8 text-center">
          <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <h3 className="font-semibold text-lg mb-2">Loading conversion...</h3>
          <p className="text-muted-foreground">Please wait while we fetch your converted document.</p>
        </Card>
      </div>
    );
  }

  if (error || !conversion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-secondary/20 flex items-center justify-center">
        <Card className="p-8 text-center max-w-md">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="font-semibold text-lg mb-2">Conversion Not Found</h3>
          <p className="text-muted-foreground mb-6">
            {error || 'The requested conversion could not be found or is not yet available.'}
          </p>
          <Button onClick={() => window.location.href = '/'} className="bg-gradient-to-r from-primary to-accent">
            <Home className="w-4 h-4 mr-2" />
            Go Home
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header Bar */}
      <header className="border-b bg-gradient-to-r from-card to-secondary/30 p-4">
        <div className="container mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FileText className="w-6 h-6 text-primary" />
            <div>
              <h1 className="font-semibold text-lg">{conversion.original_filename}</h1>
              <p className="text-sm text-muted-foreground">
                Converted on {new Date(conversion.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
          <Button 
            variant="outline" 
            onClick={() => window.location.href = '/'}
            className="bg-background/50"
          >
            <Home className="w-4 h-4 mr-2" />
            Back to Converter
          </Button>
        </div>
      </header>

      {/* HTML Content */}
      <main className="w-full">
        {conversion.html_content ? (
          <div
            className="w-full"
            dangerouslySetInnerHTML={{ __html: conversion.html_content }}
            style={{
              minHeight: 'calc(100vh - 80px)', // Account for header height
            }}
          />
        ) : (
          <div className="flex items-center justify-center p-12">
            <Card className="p-8 text-center">
              <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
              <h3 className="font-semibold text-lg mb-2">No Content Available</h3>
              <p className="text-muted-foreground">
                The HTML content for this conversion is not available.
              </p>
            </Card>
          </div>
        )}
      </main>
    </div>
  );
};

export default ViewConversion;